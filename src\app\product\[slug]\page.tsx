import type { Metadata } from "next";

import { generateProductMetadataFromSources } from "@/lib/metadata-utils";
import { fetchProductBySlug } from "@/services/product-detail-api";
import ProductDetailClient from "../clientView";
import { getProductSeo } from "@/services/seo-api";
import { stripHtml } from "@/lib/seo-utils";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  try {
    // Get the slug from params
    const { slug } = await params;

    // Fetch product details using the slug
    const product = await fetchProductBySlug(slug);

    if (product) {
      // Build metadata using product JSON SEO first, then XML, then final fallbacks
      return generateProductMetadataFromSources(product as any);
    }
  } catch (error) {
    console.error("Error generating product metadata:", error);
  }

  // Fallback metadata
  return {
    title: "Product Details - EZeats",
  };
}

// product detail page component
export default async function ProductDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  // ==============================|| API calls ||============================== //

  const { slug } = await params;
  let product = null;
  let error = null;
  let h1 = null;

  try {
    // fetch product details using the slug
    product = await fetchProductBySlug(slug);

    if (product) {
      // Get the H1 using new fallback order: product.seo.h1 -> XML h1 -> product name
      const xmlSeo = await getProductSeo(
        product.name,
        stripHtml(product.description) || ""
      );
      const fromProduct = product.seo?.h1?.trim?.() || "";
      const fromXml = xmlSeo?.h1?.trim?.() || "";
      h1 = fromProduct || fromXml || product.name;
    } else {
      error = "Product not found";
    }
  } catch (err) {
    console.error("Error loading product:", err);
    error = "Failed to load product details";
  }

  // ==============================|| UI ||============================== //

  return (
    <>
      {/* hidden h1 for SEO */}
      {h1 && <h1 className="sr-only">{h1}</h1>}

      <ProductDetailClient product={product} error={error} productId={slug} />
    </>
  );
}
