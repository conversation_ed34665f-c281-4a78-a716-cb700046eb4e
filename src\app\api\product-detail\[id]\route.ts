import { NextRequest, NextResponse } from "next/server";

import { callProductDetailAPI } from "@/lib/apiConfigs";

// get single product detail API route handler
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // get the product ID from params
    const { id: productId } = await params;

    if (!productId) {
      return NextResponse.json(
        { data: null, meta: null, error: "Product ID is required" },
        { status: 400 }
      );
    }

    // function to call the get product detail API
    const { data, error } = await callProductDetailAPI(productId);

    // verify if error
    if (error) {
      // return error response
      return NextResponse.json(
        { data: null, meta: null, error },
        { status: 500 }
      );
    }

    if (!data) {
      return NextResponse.json(
        { data: null, meta: null, error: "Product not found" },
        { status: 404 }
      );
    }

    // return success response with product detail
    return NextResponse.json(
      { data, meta: null, error: null },
      { status: 200 }
    );
  } catch (error) {
    // return error response
    return NextResponse.json(
      { data: null, meta: null, error },
      { status: 500 }
    );
  }
}
